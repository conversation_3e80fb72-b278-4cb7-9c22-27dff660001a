<?php
[
	'[^wp-json/?$]' => 'index.php?rest_route=/',
	'[^wp-json/(.*)?]' => 'index.php?rest_route=/$matches[1]',
	'[^index.php/wp-json/?$]' => 'index.php?rest_route=/',
	'[^index.php/wp-json/(.*)?]' => 'index.php?rest_route=/$matches[1]',
	'[^wp-sitemap\.xml$]' => 'index.php?sitemap=index',
	'[^wp-sitemap\.xsl$]' => 'index.php?sitemap-stylesheet=sitemap',
	'[^wp-sitemap-index\.xsl$]' => 'index.php?sitemap-stylesheet=index',
	'[^wp-sitemap-([a-z]+?)-([a-z\d_-]+?)-(\d+?)\.xml$]' => 'index.php?sitemap=$matches[1]&sitemap-subtype=$matches[2]&paged=$matches[3]',
	'[^wp-sitemap-([a-z]+?)-(\d+?)\.xml$]' => 'index.php?sitemap=$matches[1]&paged=$matches[2]',
	'[(?i)^ru$]' => 'index.php?pagename=shop', // need 
	'[(?i)^en$]' => 'index.php?pagename=shop', // need 
	'[(?i)^uk$]' => 'index.php?pagename=shop', // need 
	'[(?i)^hwtest$]' => 'index.php?pagename=hwtest',
	'[robots\.txt$]' => 'index.php?robots=1', // need 
	'[favicon\.ico$]' => 'index.php?favicon=1', // need 
	'[sitemap\.xml]' => 'index.php??sitemap=index', // need WHY 2 '??' ?
	'[.*wp-(atom|rdf|rss|rss2|feed|commentsrss2)\.php$]' => 'index.php?feed=old',
	'[.*wp-app\.php(/.*)?$]' => 'index.php?error=403',
	'[.*wp-register.php$]' => 'index.php?register=true',
	'[.?.+?/attachment/([^/]+)/?$]' => 'index.php?attachment=$matches[1]',
	'[.?.+?/attachment/([^/]+)/trackback/?$]' => 'index.php?attachment=$matches[1]&tb=1',
	'[.?.+?/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$]' => 'index.php?attachment=$matches[1]&feed=$matches[2]',
	'[.?.+?/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$]' => 'index.php?attachment=$matches[1]&feed=$matches[2]',
	'[.?.+?/attachment/([^/]+)/comment-page-([0-9]{1,})/?$]' => 'index.php?attachment=$matches[1]&cpage=$matches[2]',
	'[.?.+?/attachment/([^/]+)/embed/?$]' => 'index.php?attachment=$matches[1]&embed=true',
	'[(.?.+?)/embed/?$]' => 'index.php?pagename=$matches[1]&embed=true',
	'[(.?.+?)/trackback/?$]' => 'index.php?pagename=$matches[1]&tb=1',
	'[(.?.+?)/feed/(feed|rdf|rss|rss2|atom)/?$]' => 'index.php?pagename=$matches[1]&feed=$matches[2]',
	'[(.?.+?)/(feed|rdf|rss|rss2|atom)/?$]' => 'index.php?pagename=$matches[1]&feed=$matches[2]',
	'[(.?.+?)/page/?([0-9]{1,})/?$]' => 'index.php?pagename=$matches[1]&paged=$matches[2]',
	'[(.?.+?)/comment-page-([0-9]{1,})/?$]' => 'index.php?pagename=$matches[1]&cpage=$matches[2]',
	'[(.?.+?)(?:/([0-9]+))?/?$]' => 'index.php?pagename=$matches[1]&page=$matches[2]'
]
?>