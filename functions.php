<?php

global $current_uri; // uri with query, no domain, no domain prefix, i.e. "/en/shop/fern?adasd=123"
global $current_uri_no_q; // uri no query, no domain, no domain prefix, i.e. "/en/shop/fern", even if there's query like "?adasd=123"
global $current_uri_no_q_or_lang; // uri no query, no domain, no domain prefix, no lang, i.e. "/shop/fern", even if there's query like "?adasd=123"
global $current_uri_full; // uri without domain_prefix, i.e. "haworth.test/en/shop/fern?adasd=123", but without http:// or https://
global $q_string; // все після урли -- ?qwe=basf
global $domain_prefix; // http:// or https://
global $domain; // domain w/o prefix, i.e. "haworth.test", without http:// or https://
global $protocol_prefix;

global $lang_arr;
global $current_locale;
global $current_lang;
global $locale_names_array;

global $pages_arr;
global $post_ids_arr;
global $is_product;
global $is_shop_home;
global $is_site_local;
global $current_page;

global $sitemap_arr;
global $global_fields_arr; // metaboxes
global $sitemap_names_arr;

$pages_arr = ['home', 'shop'];

//----------- DB -----------//

require 'inc/functions/db.php';

//----------- URI -----------//

require 'inc/functions/uri.php';

//----------- LOCALE & LANGUAGE -----------//

require 'inc/functions/lang.php';

//----------- REWRITES & REDIRECTS -----------//

require 'inc/functions/rewrites.php';

//----------- HEAD -----------//

require 'inc/functions/head.php';

//----------- POST TYPES -----------//

require 'inc/functions/posts.php';

//----------- META BOXES -----------//

if (is_admin()) require 'inc/functions/metaboxes_global.php';
if (is_admin()) require 'inc/functions/metaboxes_data.php';
if (is_admin()) require 'inc/functions/metaboxes_smol.php';

//----------- SCRIPTS AND STYLES -----------//

require 'inc/functions/ss.php';

//----------- CUSTOM MENU -----------//

require 'inc/functions/menu.php';

//----------- AJAX -----------//

require 'inc/functions/ajax.php';

//----------- ADMIN -----------//

require 'inc/functions/admin.php';

//----------- SHOP -----------//

require 'inc/functions/shop.php';

// delete_post_meta(2, "test");
// add_post_meta(2, "test", $misc_meta, true);
