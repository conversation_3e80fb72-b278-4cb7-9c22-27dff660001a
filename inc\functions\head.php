<?php

function remove_redundant_shortlink() {
	// remove HTML meta tag
	// <link rel='shortlink' href='http://example.com/?p=25' />
	remove_action('wp_head', 'wp_shortlink_wp_head', 10);

	// remove HTTP header
	// Link: <https://example.com/?p=25>; rel=shortlink
	remove_action('template_redirect', 'wp_shortlink_header', 11);
}

add_filter('after_setup_theme', 'remove_redundant_shortlink');

remove_action('wp_head', 'rel_canonical');

remove_action('wp_head', 'rsd_link');

remove_action('wp_head', 'rest_output_link_wp_head', 10);

remove_action('wp_head', 'wp_oembed_add_discovery_links', 10);

remove_action('wp_head', 'feed_links_extra', 3); // Display the links to the extra feeds such as category feeds

remove_action('wp_head', 'feed_links', 2); // Display the links to the general feeds: Post and Comment Feed

remove_action('wp_head', 'wlwmanifest_link'); // Display the link to the Windows Live Writer manifest file.

remove_action('wp_head', 'index_rel_link'); // index link

remove_action('wp_head', 'parent_post_rel_link', 10, 0); // prev link

remove_action('wp_head', 'start_post_rel_link', 10, 0); // start link

remove_action('wp_head', 'adjacent_posts_rel_link', 10, 0); // Display relational links for the posts adjacent to the current post.

remove_action('wp_head', 'wp_generator'); // Display the XHTML generator that is generated on the wp_head hook, WP version

remove_action('wp_head', 'print_emoji_detection_script', 7); // remove emoji fallback from head
