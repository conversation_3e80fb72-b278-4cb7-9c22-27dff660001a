<?php get_header(); ?>

<div id="main-face">

  <div id="main-face-lang">
    <?php

      $en_class = $ru_class = $uk_class = '';

      $uk_link = 'href="' . $domain_prefix . $domain . '/uk"';
      $en_link = 'href="' . $domain_prefix . $domain . '/en"';
      $ru_link = 'href="' . $domain_prefix . $domain . '/ru"';

      if ($current_lang == 'uk') {
        $uk_class = 'active';
        $uk_link = '';
      }
      elseif ($current_lang == 'en') {
        $en_class = 'active';
        $en_link = '';
      }
      elseif ($current_lang == 'ru') {
        $ru_class = 'active';
        $ru_link = '';
      }

      echo "<a class=\"$uk_class\" {$uk_link}>УКР</a>";
      echo "<a class=\"$en_class\" {$en_link}>EN</a>";
      echo "<a class=\"$ru_class\" {$ru_link}>РУ</a>";

    ?>
  </div>

  <div id="main-face-content">

    <img id="main-face-logo" src="<?php echo wp_get_attachment_image_src($post_meta["logo_upload_onelang"][0], 'full')[0] ?>">
    <h1 id="main-face-heading"><?php echo htmlspecialchars_decode($post_meta["text_textarea_html_{$current_lang}"][0]) ?></h1>

    <a id="main-face-shop-link" href="<?php echo $domain_prefix . $domain .'/' . $current_lang . '/shop' ?>">
      <span><?php echo $post_meta["shop_link_{$current_lang}"][0] ?></span>
      <i class="fas fa-play"></i>
    </a>

    <form id="main-face-action-form">
      <p id="main-face-action-text"><?php echo $post_meta["action_text_{$current_lang}"][0] ?></p>
      <input class="generic-button" type="tel" id="main-face-action-phone" required placeholder="<?php echo $post_meta["action_placeholder_{$current_lang}"][0] ?>">
      <input class="generic-button" type="submit" id="main-face-action-submit" value="<?php echo $post_meta["action_button_{$current_lang}"][0] ?>">
    </form>

    <ul id="main-face-bullets-ul">
      <?php
        $bullets_arr = unserialize($post_meta["bullets_arr_{$current_lang}"][0]);

        foreach($bullets_arr as $val) {
          echo '<li>' . $val . '</li>';
        }
      ?>
    </ul>

    <a id="main-face-catalog" target="_blank" href="<?php echo wp_get_attachment_url($post_meta["catalog_pdf_onelang_upload"][0]) ?>">
      <div id="main-face-catalog-pic" style="background-image: url(<?php echo wp_get_attachment_image_src($post_meta["catalog_onelang_upload"][0], 'full')[0] ?>)"></div>
      <p id="main-face-catalog-text"><?php echo $post_meta["catalog_text_{$current_lang}"][0] ?></p>
      <div id="main-face-catalog-link"><i class="fas fa-download"></i></div>
    </a>

    <div id="main-face-links-div">

      <?php

      if ($post_meta["group_links"][0]) {
        $group_face_links_arr = unserialize($post_meta["group_links"][0]);

        foreach ($group_face_links_arr as $key => $value) {
          echo "<div><a class=\"main-face-link\" id=\"{$value["link_id_onelang"]}\" target=\"_blank\" href=\"{$value["href_onelang"]}\">" . "<i class=\"{$value["fa_icon_onelang"]}\"></i>" . $value["link_onelang"] . '</a></div>';
        }
      }

      ?>

    </div>

  </div>


  <div id="main-face-bgs">

  <?php

    if ($post_meta["group_bgs"][0]) {
      $group_bgs_arr = unserialize($post_meta["group_bgs"][0]);

      $i = 0;

      foreach ($group_bgs_arr as $key => $value) {
        if ($i == 0) $active_class = 'on';
        else $active_class = 'off';
        echo "<div class=\"main-face-bg-item $active_class\" id=\"main-face-bg-item-{$i}\" style=\"background-image: url(" . wp_get_attachment_image_src($value["bg_onelang_upload"], 'full')[0] . ')"></div>';
        $i++;
      }
    }

  ?>

  </div>

  <div id="main-face-poonks">
    <?php
      for ($k = 0; $k < $i; $k++) {
        if ($k == 0) $class = 'current on';
        else $class = 'off';
        echo "<a class=\"main-face-poonk {$class}\" id=\"main-face-poonk-{$k}\"><div class=\"main-face-poonk-bar-out\"><div class=\"main-face-poonk-bar\"></div></div></a>";
      }
    ?>
  </div>

</div>

<?php get_footer();
