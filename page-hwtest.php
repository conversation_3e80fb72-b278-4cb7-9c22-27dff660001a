<?php

global $current_uri;
global $current_uri_no_q;
global $current_uri_full;
global $q_string;
global $functions_server;
global $current_uri_full;
global $domain;
global $domain_prefix;
global $test_var;
global $test_var_1;
global $test_var_2;
global $test_var_3;
global $test_var_4;
global $test_var_5;
global $test_var_6;
global $test_var_7;
global $test_var_8;
global $test_var_9;
global $post_ids_arr;
global $wpdb;

global $url;
global $path;
global $orig_scheme;
global $blog_id;

global $misc_meta;
// $misc_meta = get_post_meta($post_ids_arr['misc']);

global $wp_rewrite;

echo '<pre>';
print_r($wp_rewrite->rules);
echo '</pre>';

// $wpdb->tables[] = "orders";
// $wpdb->wp_test_posts_1 = "wp_test_posts_1";
// $wpdb->wp_test_posts_2 = "wp_test_posts_2";
// $wpdb->wp_posts_bk = "wp_posts_bk";
//
// global $wpdb;
//
// $sync_tables_arr = ['wp_posts', 'wp_postmeta', 'wp_orders'];
//
// foreach ($sync_tables_arr as $value) {
//
//   if (!isset($wpdb->tables[$value])) {
//     $wpdb->tables[] = $value;
//     $wpdb->$value = $value;
//   }
//
//   $last_bk_name = $value . '_json_bk';
//   $json_bk_name = $value . '_last_bk';
//
//   $wpdb->tables[] = $last_bk_name;
//   $wpdb->tables[] = $json_bk_name;
//
//   $wpdb->$last_bk_name = $last_bk_name;
//   $wpdb->$json_bk_name = $json_bk_name;
//
// }

// require 'inc/functions/metaboxes_smol.php';

// sync_db_wpdb_add_tables_func();

// $data = sync_db_get_table_columns_names_func('orders');

// $test1 = get_post_meta(2, 'test')[0];


$my_arr = [];

$my_arr['foo'] = [];
$my_arr['bar'] = [];
$my_arr['buz'] = [];

// var_dump($my_arr);
// var_dump($my_arr);
// phpinfo();

// echo '<pre>';

// foreach ($my_arr as $key => $arr) {
//   echo $key;
//   echo '<br>';
// }
// print_r($my_arr);

// echo $json_bk_status;
// print_r($wpdb->$table_name);

// print_r($test1);
// foreach($test1 as $table_name) {
//   echo $table_name;
//   echo '<br>';
// }

// echo '</pre>';


// $table_name = 'orders';
//
// $json_bk_table_name = $table_name . '_json_bk';
//
// $columns_names_string = 'order_bk_json, order_bk_autoincrement_value';
// $values_string = '%s, %d';
//
// // echo $wpdb->$table_name;
//
// $table_data = sync_db_get_table_data_func($table_name);
//
// $table_name_for_q = $wpdb->$json_bk_table_name;
//
// $json_bk_status = $wpdb->query(
//   $wpdb->prepare(
//     "INSERT INTO $table_name_for_q
//     ($columns_names_string)
//     VALUES($values_string)",
//     $table_data['content_json'], $table_data['auto_increment_value']
//   )
// );

// $json_bk_status = $wpdb->query(
//   $wpdb->prepare(
//     "INSERT INTO wp_orders_json_bk
//     ($columns_names_string)
//     VALUES($values_string)",
//     $table_data['content_json'], $table_data['auto_increment_value']
//   )
// );




// $arr = ['posts', 'postmeta', 'orders'];

// $test_json = 'aaaaaaaaaaaaaasddas\ a\sd as d ada';

// $test_decode = json_decode($test_json);

// $json = '[asd: adsa]';

// $valid = json_validate($json);

// $content = sync_db_get_table_data_func('postmeta');
// $json = json_encode($content['content']);
// $dc = json_decode($json);

// $dbq = get_post_meta(51, 'group_footer', true);

// $json = json_encode($dbq);
//
// $esc = esc_html('<div class="class"></div>');
//

// $string = '"';
// $esc = esc_html($string);


// delete_post_meta(2, "test");
// add_post_meta(2, "test", $esc, true);

// $esc2 = '&quot;';


// $try_decode = json_decode($json, true);

// $dbq = $wpdb->get_results(
//   "SELECT * FROM $wpdb->postmeta WHERE meta_key = 'group_footer'", 'ARRAY_A'
// );

// $dbq_2 = $dbq[0]['meta_value'];

// $decode = json_decode($dbq_2);


echo '<pre>';

// echo htmlspecialchars($esc2);

  // if ($dc == NULL) print_r($dc);

  // print_r($esc);


  // print_r($wpdb->tables);
  // print_r($wpdb);
  // unset($arr['posts']);
  // print_r($arr);

  // if ($test_decode == NULL) print_r('null');

  // echo is_null($test_decode);

  // print_r(gettype($test_decode));

  // print_r($dbq);

echo '</pre>';


// %d, %d, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %d, %s, %d, %s, %s, %d
// ID, post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt, post_status, comment_status, ping_status, post_password, post_name, to_ping, pinged, post_modified, post_modified_gmt, post_content_filtered, post_parent, guid, menu_order, post_type, post_mime_type, comment_count


// $dbq = $wpdb->get_row(
//   $wpdb->prepare(
//     "SELECT * FROM $wpdb->orders"
//   )
// );

// $dbq = $wpdb->get_results(
//   "SELECT * FROM $wpdb->orders WHERE DATE(order_date) = CURDATE()", 'ARRAY_A'
// );
// $dbq = array();

// $dbq = $wpdb->get_results(
//   "SELECT * FROM $wpdb->wp_test_posts_1", 'ARRAY_A'
// );

// $auto_increment_value = $wpdb->get_var(
//   "SELECT `AUTO_INCREMENT` FROM information_schema.TABLES WHERE TABLE_NAME = 'wp_test_posts_1'"
// );
//
// $dbq = $wpdb->get_results(
//   "SELECT * FROM $wpdb->wp_test_posts_1", 'ARRAY_A'
// );
//
// $columns = $wpdb->get_results(
//   // "SELECT `COLUMN_NAME` FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'wp_test_posts_1'", 'ARRAY_A'
//   // "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'wp_test_posts_1' ORDER BY `ORDINAL_POSITION`", 'ARRAY_A'
//   // "SELECT `COLUMN_NAME` FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'wp_test_posts_1' ORDER BY `ORDINAL_POSITION`", 'ARRAY_A'
//   "SHOW COLUMNS FROM wp_test_posts_1", 'ARRAY_A'
// );

// $columns_merged = [];

// foreach ($columns as $arr_order_key => $data_arr) {
//   $columns_merged[] = $data_arr['Field'];
// }

// $columns_string = implode(", ",array_values($columns_merged));
// $columns_string = implode(", ", $columns_merged);

// function sync_db_get_table_column_names_func($table) {
//
//   global $wpdb;
//
//   $columns = $wpdb->get_results(
//     // "SELECT `COLUMN_NAME` FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'wp_test_posts_1'", 'ARRAY_A'
//     // "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'wp_test_posts_1' ORDER BY `ORDINAL_POSITION`", 'ARRAY_A'
//     // "SELECT `COLUMN_NAME` FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'wp_test_posts_1' ORDER BY `ORDINAL_POSITION`", 'ARRAY_A'
//     "SHOW COLUMNS FROM {$table}", 'ARRAY_A'
//   );
//
//   $values_arr = [];
//   $columns_names_arr = [];
//
//   $columns_data = [];
//
//   foreach ($columns as $arr_order_key => $data_arr) {
//     $columns_names_arr[] = $data_arr['Field'];
//     $values_arr[] = '%s';
//   }
//
//   $columns_data['columns_names_string'] = implode(", ", $columns_names_arr);
//   $columns_data['values_string'] = implode(", ", $values_arr);
//
//   return $columns_data;
// }
//
// $cd = sync_db_get_table_column_names_func('wp_test_posts_2');

// $columns_merged = array_merge($columns);

// $dbq = $wpdb->get_results(
//   "SELECT * FROM $wpdb->wp_posts_bk WHERE post_bk_id = 1", 'ARRAY_A'
// );

// $data = json_decode($dbq[0]['post_bk_json'], true);


//
// $auto_increment_value = $wpdb->get_results(
//   "SELECT AUTO_INCREMENT FROM $wpdb->wp_test_posts_1"
// );

echo '<pre>';

// print_r($cd);
// print_r($columns_merged);
// echo $string;

// foreach ($dbq[0] as $key => $value) {
//   echo $value . ' = ' . gettype($value);
//   echo '<pre>';
// }

// print_r($dbq);

// $test1 = get_post_meta(2, 'test');

// print_r($test1);

// echo $auto_increment_value;

// print_r($auto_increment_value);



echo '<br>';

// print_r($dbq);


// $dbq = $wpdb->get_results(
//   "SELECT COUNT(*) FROM $wpdb->orders WHERE DATE(order_date) = CURDATE()", 'ARRAY_A'
// );

// $dbq = $wpdb->get_var(
//   // "SELECT COUNT(*) FROM $wpdb->orders"
//   "SELECT COUNT(*) FROM $wpdb->orders WHERE DATE(order_date) = CURDATE()"
// );

// $today_order_number = $wpdb->get_var(
//   // "SELECT COUNT(*) FROM $wpdb->orders"
//   "SELECT COUNT(*) FROM $wpdb->orders WHERE DATE(order_date) = CURDATE()"
// ) + 1;
//
// print_r($today_order_number);

// echo date("ymd") . '-' . 1;


// print_r(count($dbq));

// if (isset($dbq)) {
//
//   if (empty($dbq)) {
//     echo 'empty';
//     echo '<br>';
//     echo '<br>';
//   }
//
//
//   print_r($dbq[0]['order_id']);
// }
// else echo 'not set';



// print_r($dbq);


echo '</pre>';



// $order_content = [
//   'price' => 100,
//   'product' => 'Fern',
//   'qty' => 3
// ];
//
// $order_content_json = json_encode($order_content);
//
// $order_number = '040813-01';
// $order_last_name = 'Woo';
// $order_first_name = 'Jonny';
// $order_email = '<EMAIL>';
// $order_phone = '050-387-17-62';
// $order_city = 'Kyiv';
// $order_address = 'Nova Post 292';
// $order_comment = 'this is a test comment
// enter
//
// enter';




// $wpdb->query(
//   $wpdb->prepare(
//     "INSERT INTO $wpdb->orders
//     (order_number, order_last_name, order_first_name, order_email, order_phone, order_city, order_address, order_comment, order_content)
//     VALUES(%s, %s, %s, %s, %s, %s, %s, %s, %s)",
//     [$order_number, $order_last_name, $order_first_name, $order_email, $order_phone, $order_city, $order_address, $order_comment, $order_content_json]
//   )
// );





// $shop_settings_meta = get_post_meta($post_ids_arr['shop_settings']);

// echo '<div>';

// global

// print_r(wp_date(get_option( 'date_format' )));


// global $wpdb;
// $results = $wpdb->get_results( "SELECT * FROM {$wpdb->prefix}postmeta");


echo '<pre>';

// print_r($order_content_json);

// $testpost = get_posts(['post_type'=>'page', 'posts_per_page'=> 1, 'post_status' => ['publish', 'private' , 'draft'], 'name' => 'shop'])[0];



// print_r($wpdb->tables);
// print_r($wpdb);

// print_r($testpost);
// print_r($testpost->post_date);
// echo '<br>';
// print_r(wp_date(get_option('Y-m-d')));
// echo '<br>';
// echo time();

// echo(gettype(wp_date(get_option('date_format'))));

// print_r($results);

// $item_post_id = get_posts(['post_type'=>'shop', 'posts_per_page'=> 1, 'name' => 'feran']);

// $test1 = stripslashes(get_post_meta(2, 'test')[0]);
// $test1 = json_decode(urldecode(get_post_meta(2, 'test')[0]), true);
// $test2 = get_post_meta(2, 'test')[0];
// $test = json_decode(stripslashes(get_post_meta(2, 'test')[0]), true);
// $test = json_decode(stripslashes(get_post_meta(2, 'test')[0]));
// print_r($test1);
// echo '<br>';
// print_r($test2);

// print_r($locale);

// print_r($post);
// $var = $post_meta["product_pic_onelang_upload"][0],
// $post_meta = get_post_meta(49);
// $product_pic = wp_get_attachment_image_src($post_meta["product_pic_onelang_upload"][0], 'thumbnail');
// print_r($product_pic);

echo '</pre>';

//
// $my_bool = false;
// ob_start();
// include_once(get_template_directory() . '/email.php' );
// $email = ob_get_contents();
// ob_end_clean();
// $email = file_get_contents(get_template_directory() . '/email.php');
// echo $email;












// if($my_bool) echo 'true';

// add_post_meta(46, 'POST_test', 'asdasdasd', true);

// $test_get = get_post_meta(46, 'asdasdasdas', true);

// if (isset($test_get) && $test_get != '') $test_bool = true;
// if (isset($test_get)) $test_bool = true;
// else $test_bool = false;

// $test_bool = metadata_exists('post', 46, 'POST_test');


// print_r($test_bool);


// echo '<pre>';

// echo preg_replace('/ /', 'T', '2023-11-07 01:13:33+00:00');

// print_r($post_ids_arr);

// print_r(get_posts(['post_type'=>'shop', 'posts_per_page'=> 1, 'name' => 'fern']));

// echo preg_match('/\/en|\/ru|\/uk|\/fern/i', '/aaa/fErN');


// print_r(unserialize($shop_settings_meta['group_top_menu_links'][0]));
// echo '<br>';
//
// echo '</pre>';
//
// echo '</div>';
//
// $post = get_post_meta( 46, 'POST_test');
//
// echo '<pre>';
// print_r($post);
// echo '</pre>';
//
// echo '<pre>';
// print_r($wp_rewrite);
// echo '</pre>';

// echo "test";
// echo "<pre>";
// print_r($post_meta = get_post_meta(16));
// print_r($post_meta = get_post_meta(10));
// print_r($post_meta = get_post_meta(12));
// print_r($misc_meta);
// echo "</pre>";

// main 10, hwt 12, misc 16


























































// function test_test($arg1) {
//   return 'blasssli';
// }
//
// add_filter('test_filter_func', 'test_test');


// function stylesheet_directory_uri_func($local_var_1, $local_var_2, $local_var_3) {
//   global $test_var_1;
//   global $test_var_2;
//   global $test_var_3;
//
//   $test_var_1 = $local_var_1;
//   $test_var_2 = $local_var_2;
//   $test_var_3 = $local_var_3;
//
//   return $local_var_1;
//
// }
//
// add_filter('stylesheet_directory_uri', 'stylesheet_directory_uri_func', 10, 3);
//
//
// echo '<pre>';
//
// echo '<b>get_stylesheet_directory_uri()</b>';
// echo '<br>';
// echo '<br>';
//
// echo 'get_stylesheet_directory_uri():';
// echo '<br>';
// print_r(get_stylesheet_directory_uri());
// echo '<br>';
// echo '<br>';
//
// echo '$stylesheet_dir_uri:';
// echo '<br>';
// print_r($test_var_1);
// echo '<br>';
// echo '<br>';
//
// echo '$stylesheet:';
// echo '<br>';
// print_r($test_var_2);
// echo '<br>';
// echo '<br>';
//
// echo '$theme_root_uri:';
// echo '<br>';
// print_r($test_var_3);
// echo '<br>';
// echo '<br>';
// echo '<br>';
//
//
//
// echo '<b>get_option(\'siteurl\')</b>';
// echo '<br>';
//
// print_r(get_option( 'siteurl' ));
// echo '<br>';
// echo '<br>';
// echo '<br>';
//
//
//
//
//
//
// function theme_root_uri_func($local_var_1, $local_var_2, $local_var_3) {
//   global $test_var_4;
//   global $test_var_5;
//   global $test_var_6;
//
//   $test_var_4 = $local_var_1;
//   $test_var_5 = $local_var_2;
//   $test_var_6 = $local_var_3;
//
//   return $local_var_1;
//
// }
//
// add_filter('theme_root_uri', 'theme_root_uri_func', 10, 3);
//
//
//
//
// echo '<b>get_theme_root_uri()</b>';
// echo '<br>';
// echo '<br>';
//
// print_r('get_theme_root_uri():');
// echo '<br>';
// print_r(get_theme_root_uri());
// echo '<br>';
// echo '<br>';
//
// print_r('$theme_root_uri:');
// echo '<br>';
// print_r($test_var_4);
// echo '<br>';
// echo '<br>';
//
// print_r('get_option( \'siteurl\' ):');
// echo '<br>';
// print_r($test_var_5);
// echo '<br>';
// echo '<br>';
//
// print_r('$stylesheet_or_template:');
// echo '<br>';
// print_r($test_var_6);
// echo '<br>';
// echo '<br>';
// echo '<br>';






// function content_url_func($url, $path) {
//   global $test_var_7;
//   global $test_var_8;
//
//   $test_var_7 = $url;
//   $test_var_8 = $path;
//
//   return $url;
//
// }
//
// add_filter('content_url', 'content_url_func', 10, 2);


// echo '<b>content_url(\'themes\')</b>';
// echo '<br>';
// echo '<br>';
//
// print_r('content_url(\'themes\'):');
// echo '<br>';
// print_r(content_url('/themes'));
// echo '<br>';
// echo '<br>';
//
// print_r('$url:');
// echo '<br>';
// print_r($test_var_7);
// echo '<br>';
// echo '<br>';
//
// print_r('$path:');
// echo '<br>';
// print_r($test_var_8);
// echo '<br>';
// echo '<br>';
//
//
//
//
// function wp_get_attachment_image_src_func($image, $attachment_id, $size, $icon) {
//   global $test_var_9;
//   $test_var_9 = $image;
//
//   return $test_var_9;
// }
//
// add_filter('wp_get_attachment_image_src', 'wp_get_attachment_image_src_func', 10, 4);
//
//
//
//
//
//
// global $misc_meta;
// $misc_meta = get_post_meta($post_ids_arr['misc']);
// echo 'wp_get_attachment_image_src:';
// echo '<br>';
// print_r(wp_get_attachment_image_src($misc_meta["logo_img_upload_onelang"][0], 'full')[0]);
//
//
//
//
//
//
// echo '<br>';
// echo '<br>';
// echo '<br>';
// echo '<br>';
// echo print_r(wp_get_upload_dir());
//
//
// echo '<br>';
// echo '<br>';
//
// global $test_var_15;
// echo $test_var_15;
//
//
// echo '</pre>';



// print_r(test_filter_func());
// echo '</pre>';


// global $wp_post_types;
// echo '<pre>';
// print_r($wp_post_types);
// echo '</pre>';
//
// echo $current_uri_no_q;
//
// echo '<pre>';
// print_r($functions_server['QUERY_STRING']);
// echo '</pre>';
//
// $a = get_post_meta(2);

// global $projects_global;
// echo '<pre>';
// print_r($projects_global);
// echo '</pre>';
//
// echo '<pre>';
//
// print_r($test_var_1);
// echo '<br>';
// print_r($test_var_1);

// print_r($_SERVER);
// $a = unserialize(unserialize(wp_unslash($a['test'][0])));

// $a = ['asd', 'gfggg', 'aadssada'];
// $a = serialize($a);
// $a = unserialize($a);
// print_r($a['test']);

// $to = ['<EMAIL>'];
// $headers = array (
//   'From: <EMAIL> <<EMAIL>>',
//   'Content-type' => 'text/html; charset=UTF-8'
// );
// $body = 'test';
// print_r(wp_mail($to, 'Запрос через форму', $body, $headers));


// foreach ($a as $key => $value) {
  // print_r($value);
// }
// echo '</pre>';




//
// global $wp_rewrite;
// echo '<pre>';
// print_r($wp_rewrite);
// echo '</pre>';

// if ($_SERVER['QUERY_STRING']) echo 1;

?>
