"use strict";

const {src, dest, watch, series} = require('gulp');
const newer = require('gulp-newer');
const notify = require('gulp-notify');
const {deleteAsync} = require('del');

// styles
const sass = require('gulp-sass')(require('sass'));
const postcss = require('gulp-postcss');
const autoprefixer = require('autoprefixer');
const cssnano = require('cssnano');

// js
const uglify = require('gulp-uglify');

// Paths configuration
const paths = {
	scss: {
		src: 'dev/scss/**/*.scss',						// Source: all .scss files in scss folder and subfolders
		dest: 'css/',													// Destination: css folder
		prodDest: '_prod/css/'
	},
	js: {
		src: 'js/*.js',
		prodDest: '_prod/js/'
	},
	php: {
		src: [
			'./*.php',													// Root PHP files
			'./inc/**/*.php',										// PHP files in inc folder and subfolders
			'./shop-template-parts/**/*.php'		// PHP files in shop-template-parts
		],
		dest: './_prod'
	},
	prod: '_prod/'
};

// Compile SCSS to CSS (Development mode)
function compileSass() {
	return(
		src(paths.scss.src)
		.pipe(sass({
			style: 'expanded',	// Readable CSS for development
		})
		.on('error', sass.logError))
		.on('error', notify.onError({
			title: 'SCSS Error',
			message: '<%= error.message %>'
		}))
		.pipe(postcss([autoprefixer('last 2 versions', '> 1%')]))
		.pipe(dest(paths.scss.dest))
	);
}

// Compile SCSS to CSS (Production mode - compressed)
function minifySass() {
	return(
		src(paths.scss.src)
		.pipe(sass({
			style: 'compressed'
		})
		.on('error', sass.logError))
		.pipe(postcss([autoprefixer('last 2 versions', '> 1%')]))
		.pipe(dest(paths.scss.prodDest))
	);
}

// Compile SCSS to CSS with  (Production mode - compressed)
function nano() {
	return(
		src(paths.scss.src)
		.pipe(sass().on('error', sass.logError))
		.pipe(postcss([
			autoprefixer('last 2 versions', '> 1%'),
			cssnano()
		]))
		.pipe(dest(paths.scss.prodDest))
	);
}

function minifyJs() {
	return(
		src(paths.js.src)
		.pipe(uglify())
		.pipe(dest(paths.js.prodDest))
	);
}

// Function to copy newer PHP files while maintaining structure
function copyPhpFiles() {
	return (
		src(paths.php.src, { base: './' })
		.pipe(newer(paths.php.dest))
		.pipe(dest(paths.php.dest))
	);
}

// Watch for changes in SCSS files
function watchFiles() {
	watch(paths.scss.src, compileSass);
}

// Clean CSS directory
async function clean() {
	return await(
		deleteAsync([
			paths.scss.dest + '**/*',
			paths.scss.prodDest + '**/*',
			paths.js.prodDest + '**/*',
			paths.prod + '**/*',
		])
	);
}

exports.sass = compileSass;
exports.minifySass = minifySass;
exports.nano = nano;
exports.minifyJs = minifyJs;
exports.clean = clean;
exports.php = copyPhpFiles;
exports.prod = series(clean, nano, minifyJs, copyPhpFiles);
exports.watch = series(compileSass, watchFiles);
exports.default = series(compileSass, watchFiles);