post_rewrite_rules:

[[^/]+/attachment/([^/]+)/?$] => index.php?attachment=$matches[1]
[[^/]+/attachment/([^/]+)/trackback/?$] => index.php?attachment=$matches[1]&tb=1
[[^/]+/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?attachment=$matches[1]&feed=$matches[2]
[[^/]+/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$] => index.php?attachment=$matches[1]&feed=$matches[2]
[[^/]+/attachment/([^/]+)/comment-page-([0-9]{1,})/?$] => index.php?attachment=$matches[1]&cpage=$matches[2]
[[^/]+/attachment/([^/]+)/embed/?$] => index.php?attachment=$matches[1]&embed=true
[([^/]+)/embed/?$] => index.php?name=$matches[1]&embed=true
[([^/]+)/trackback/?$] => index.php?name=$matches[1]&tb=1
[([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?name=$matches[1]&feed=$matches[2]
[([^/]+)/(feed|rdf|rss|rss2|atom)/?$] => index.php?name=$matches[1]&feed=$matches[2]
[([^/]+)/page/?([0-9]{1,})/?$] => index.php?name=$matches[1]&paged=$matches[2]
[([^/]+)/comment-page-([0-9]{1,})/?$] => index.php?name=$matches[1]&cpage=$matches[2]
[([^/]+)(?:/([0-9]+))?/?$] => index.php?name=$matches[1]&page=$matches[2]
[[^/]+/([^/]+)/?$] => index.php?attachment=$matches[1]
[[^/]+/([^/]+)/trackback/?$] => index.php?attachment=$matches[1]&tb=1
[[^/]+/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?attachment=$matches[1]&feed=$matches[2]
[[^/]+/([^/]+)/(feed|rdf|rss|rss2|atom)/?$] => index.php?attachment=$matches[1]&feed=$matches[2]
[[^/]+/([^/]+)/comment-page-([0-9]{1,})/?$] => index.php?attachment=$matches[1]&cpage=$matches[2]
[[^/]+/([^/]+)/embed/?$] => index.php?attachment=$matches[1]&embed=true

author_rewrite_rules:

[author/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?author_name=$matches[1]&feed=$matches[2]
[author/([^/]+)/(feed|rdf|rss|rss2|atom)/?$] => index.php?author_name=$matches[1]&feed=$matches[2]
[author/([^/]+)/embed/?$] => index.php?author_name=$matches[1]&embed=true
[author/([^/]+)/page/?([0-9]{1,})/?$] => index.php?author_name=$matches[1]&paged=$matches[2]
[author/([^/]+)/?$] => index.php?author_name=$matches[1]

tag_rewrite_rules:

[tag/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?tag=$matches[1]&feed=$matches[2]
[tag/([^/]+)/(feed|rdf|rss|rss2|atom)/?$] => index.php?tag=$matches[1]&feed=$matches[2]
[tag/([^/]+)/embed/?$] => index.php?tag=$matches[1]&embed=true
[tag/([^/]+)/page/?([0-9]{1,})/?$] => index.php?tag=$matches[1]&paged=$matches[2]
[tag/([^/]+)/?$] => index.php?tag=$matches[1]

date_rewrite_rules:

[([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&feed=$matches[4]
[([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/(feed|rdf|rss|rss2|atom)/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&feed=$matches[4]
[([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/embed/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&embed=true
[([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/page/?([0-9]{1,})/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&paged=$matches[4]
[([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]
[([0-9]{4})/([0-9]{1,2})/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&feed=$matches[3]
[([0-9]{4})/([0-9]{1,2})/(feed|rdf|rss|rss2|atom)/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&feed=$matches[3]
[([0-9]{4})/([0-9]{1,2})/embed/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&embed=true
[([0-9]{4})/([0-9]{1,2})/page/?([0-9]{1,})/?$] => index.php?year=$matches[1]&monthnum=$matches[2]&paged=$matches[3]
[([0-9]{4})/([0-9]{1,2})/?$] => index.php?year=$matches[1]&monthnum=$matches[2]
[([0-9]{4})/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?year=$matches[1]&feed=$matches[2]
[([0-9]{4})/(feed|rdf|rss|rss2|atom)/?$] => index.php?year=$matches[1]&feed=$matches[2]
[([0-9]{4})/embed/?$] => index.php?year=$matches[1]&embed=true
[([0-9]{4})/page/?([0-9]{1,})/?$] => index.php?year=$matches[1]&paged=$matches[2]
[([0-9]{4})/?$] => index.php?year=$matches[1]

root_rewrite_rules:

[feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?&feed=$matches[1]
[(feed|rdf|rss|rss2|atom)/?$] => index.php?&feed=$matches[1]
[embed/?$] => index.php?&embed=true
[page/?([0-9]{1,})/?$] => index.php?&paged=$matches[1]

comments_rewrite_rules:

[comments/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?&feed=$matches[1]&withcomments=1
[comments/(feed|rdf|rss|rss2|atom)/?$] => index.php?&feed=$matches[1]&withcomments=1
[comments/embed/?$] => index.php?&embed=true

search_rewrite_rules:

[search/(.+)/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?s=$matches[1]&feed=$matches[2]
[search/(.+)/(feed|rdf|rss|rss2|atom)/?$] => index.php?s=$matches[1]&feed=$matches[2]
[search/(.+)/embed/?$] => index.php?s=$matches[1]&embed=true
[search/(.+)/page/?([0-9]{1,})/?$] => index.php?s=$matches[1]&paged=$matches[2]
[search/(.+)/?$] => index.php?s=$matches[1]

category_rewrite_rules:

[category/(.+?)/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?category_name=$matches[1]&feed=$matches[2]
[category/(.+?)/(feed|rdf|rss|rss2|atom)/?$] => index.php?category_name=$matches[1]&feed=$matches[2]
[category/(.+?)/embed/?$] => index.php?category_name=$matches[1]&embed=true
[category/(.+?)/page/?([0-9]{1,})/?$] => index.php?category_name=$matches[1]&paged=$matches[2]
[category/(.+?)/?$] => index.php?category_name=$matches[1]

post_tag_rewrite_rules:

[type/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$] => index.php?post_format=$matches[1]&feed=$matches[2]
[type/([^/]+)/(feed|rdf|rss|rss2|atom)/?$] => index.php?post_format=$matches[1]&feed=$matches[2]
[type/([^/]+)/embed/?$] => index.php?post_format=$matches[1]&embed=true
[type/([^/]+)/page/?([0-9]{1,})/?$] => index.php?post_format=$matches[1]&paged=$matches[2]
[type/([^/]+)/?$] => index.php?post_format=$matches[1]