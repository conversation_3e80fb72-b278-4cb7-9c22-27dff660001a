<?php

global $post_ids_arr;

$post_ids_arr = [
  'misc' => get_posts(['post_type' => 'misc'])[0]->ID,
  'home' => get_posts(['post_type'=>'page', 'posts_per_page'=> 1, 'post_status' => ['publish', 'private' , 'draft'], 'name' => 'home'])[0]->ID,
  'hwtest' => get_posts(['post_type'=>'page', 'posts_per_page'=> 1, 'post_status' => ['publish', 'private' , 'draft'], 'name' => 'hwtest'])[0]->ID,
  'shop' => get_posts(['post_type'=>'page', 'posts_per_page'=> 1, 'post_status' => ['publish', 'private' , 'draft'], 'name' => 'shop'])[0]->ID,
  'shop_settings' => get_posts(['post_type' => 'shop_settings'])[0]->ID,
  'shop_orders' => get_posts(['post_type' => 'shop_orders'])[0]->ID,
  'db_sync' => get_posts(['post_type' => 'db_sync'])[0]->ID,
  'product_fern' => get_posts(['post_type'=>'shop', 'posts_per_page'=> 1, 'name' => 'fern'])[0]->ID,
  'product_soji_black' => get_posts(['post_type'=>'shop', 'posts_per_page'=> 1, 'name' => 'soji-black'])[0]->ID,
  'product_soji_white' => get_posts(['post_type'=>'shop', 'posts_per_page'=> 1, 'name' => 'soji-white'])[0]->ID,
];

//----------- REG CUSTOM POSTS -----------//

function reg_post_types_func() {

  register_post_type('shop', [
    'label' => 'Shop',
    'supports' => ['title', 'comments'],
    'public' => true,
    'show_ui' => true,
    'query_var' => false,
    'has_archive' => false,
    'show_in_menu' => true,
    'has_archive' => false,
    'labels' => [
      'name' => 'Products',
      'singular_name' => 'Product',
    ],
    'rewrite' => false,
    'capability_type' => 'post',

    'menu_icon' => 'dashicons-book'
  ]);

  register_post_type('misc', [
    'label' => 'Misc Settings',
    'supports' => [''],
    'public' => false,
    'rewrite' => false,
    'show_ui' => true,
    'show_in_menu' => false,
    'query_var' => false,
    'map_meta_cap' => true,
    'capabilities' => [
     'create_posts' => 'do_not_allow'
    ],
    'labels' => [
     'edit_item' => 'Modify Site Elements'
    ]
  ]);

  register_post_type('shop_settings', [
    'label' => 'Shop Settings',
    'supports' => [''],
    'public' => false,
    'rewrite' => false,
    'show_ui' => true,
    'show_in_menu' => false,
    'query_var' => false,
    'map_meta_cap' => true,
    'capabilities' => [
     'create_posts' => 'do_not_allow'
    ],
    'labels' => [
     'edit_item' => 'Change Shop Settings'
    ]
  ]);

  register_post_type('shop_orders', [
    'label' => 'Orders',
    'supports' => [''],
    // 'supports' => ['title'],
    'public' => false,
    'rewrite' => false,
    'show_ui' => true,
    'show_in_menu' => false,
    'query_var' => false,
    'map_meta_cap' => true,
    'capabilities' => [
     'create_posts' => 'do_not_allow'
    ],
    'labels' => [
      'edit_item' => 'Shop Orders'
    ]
  ]);

  register_post_type('db_sync', [
    'label' => 'Sync DB ',
    'supports' => [''],
    // 'supports' => ['title'],
    'public' => false,
    'rewrite' => false,
    'show_ui' => true,
    'show_in_menu' => false,
    'query_var' => false,
    'map_meta_cap' => true,
    'capabilities' => [
     'create_posts' => 'do_not_allow'
    ],
    'labels' => [
      'edit_item' => 'Sync DB'
    ]
  ]);

  // register_post_type('shop_orders', [
  //   'label' => 'Orders',
  //   'supports' => [''],
  //   'public' => false,
  //   'rewrite' => false,
  //   'show_ui' => true,
  //   'show_in_menu' => true,
  //   'exclude_from_search' => true,
  //   'query_var' => false,
  //   'publicly_queryable' => false,
  //   'has_archive' => false,
  //   'hierarchical' => false,
  //   'show_in_nav_menus' => false,
  //   'labels' => [
  //     'name' => 'Orders',
  //     'singular_name' => 'Order',
  //     'add_new' => 'Add order',
  //     'add_new_item' => 'Add new order'
  //   ],
  //   'menu_icon' => 'dashicons-archive'
  // ]);

  // old backup delete !!! temp

}

add_action('init', 'reg_post_types_func');


//----------- POST TYPES FUNCTIONS -----------//


function remove_page_supports() {

  global $post_ids_arr;

  if (isset($_GET['post'])) if ($_GET['post']) {

    $postid = $_GET['post'];

    if ($postid == $post_ids_arr['home'] || $postid == $post_ids_arr['hwtest'] || $postid == $post_ids_arr['shop']) {
      remove_post_type_support( 'page' , 'editor' );
      remove_post_type_support( 'page' , 'hierarchical' );
      remove_post_type_support( 'page' , 'comments' );
      remove_post_type_support( 'page' , 'custom-fields' );
      remove_post_type_support( 'page' , 'page-attributes' );
      remove_post_type_support( 'page' , 'author' );
    }
  }
}

add_action( 'admin_init', 'remove_page_supports' );

$role = get_role('administrator');
$role->remove_cap('delete_published_pages');
$role->remove_cap('delete_themes');

?>
